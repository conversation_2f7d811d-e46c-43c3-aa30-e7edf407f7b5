import {
  Controller,
  Get,
  Post,
  Patch,
  Body,
  Param,
  UseGuards,
  Req,
  Query,
  ForbiddenException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import {CreateUserDto, EUserRole} from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { FilterUserDto } from './dto/filter-user.dto';
import { UserService } from './user.service';
import { AuthGuard } from '../auth/guards/auth.guard';
import { RoleGuard } from '../auth/guards/role.guard';
import { Roles } from '../auth/decorators/role.decorator';
import { ActiveUser } from '../auth/decorators/active-user.decorator';
import { RbacExceptions } from '../auth/exceptions/rbac-exceptions';
import { RbacService } from '../auth/services/rbac.service';

@ApiTags('User')
@ApiBearerAuth()
@Controller('user')
@UseGuards(AuthGuard, RoleGuard) // Apply AuthGuard and RoleGuard to all routes in this controller
export class UserController {
  constructor(
    private readonly userService: UserService,
    private readonly rbacService: RbacService,
  ) {}

  @Post()
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER)
  @ApiOperation({ summary: 'Create a new user' })
  @ApiBody({ type: CreateUserDto })
  @ApiResponse({
    status: 201,
    description: 'User created successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        name: { type: 'string' },
        email: { type: 'string' },
        role: { type: 'string', enum: ['teacher', 'admin'] },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  create(@Body() createUserDto: CreateUserDto, @Req() req) {
    // Business logic: School managers can only create users for their own school
    if (this.rbacService.isSchoolManager(req.user)) {
      // Validate school manager has assigned school
      if (!req.user.schoolId) {
        throw RbacExceptions.schoolManagerNoSchool();
      }

      // Validate role restrictions
      this.rbacService.validateSchoolManagerUserCreation(req.user, createUserDto.role);

      // Validate school assignment
      this.rbacService.validateSchoolManagerUserAssignment(req.user, createUserDto.schoolId);

      // If no schoolId provided, assign to manager's school
      if (!createUserDto.schoolId) {
        createUserDto.schoolId = req.user.schoolId;
      }
    }

    return this.userService.create(createUserDto);
  }

  @Get('me')
  @ApiOperation({ summary: 'Get current user profile' })
  @ApiResponse({
    status: 200,
    description: 'Current user profile retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        name: { type: 'string' },
        email: { type: 'string' },
        role: { type: 'string', enum: ['teacher', 'admin'] },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async me(@ActiveUser() user) {
    const sub = user.sub; // Extract user ID from the access token
    return this.userService.findById(sub);
  }

  @Get()
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER)
  @ApiOperation({ summary: 'Get all users with optional filtering' })
  @ApiQuery({
    name: 'schoolId',
    required: false,
    type: String,
    description: 'Filter users by school ID (UUID format)'
  })
  @ApiQuery({
    name: 'role',
    required: false,
    enum: EUserRole,
    description: 'Filter users by role'
  })
  @ApiResponse({
    status: 200,
    description: 'Users retrieved successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          name: { type: 'string' },
          email: { type: 'string' },
          role: { type: 'string', enum: ['teacher', 'admin', 'student', 'school_manager'] },
          schoolId: { type: 'string', nullable: true },
        },
      },
    },
  })
  async findAll(@Query() filterUserDto: FilterUserDto, @Req() req) {
    // Business logic: School managers can only access users from their own school
    if (this.rbacService.isSchoolManager(req.user)) {
      if (!req.user.schoolId) {
        throw RbacExceptions.schoolManagerNoSchool();
      }
      // Override any schoolId filter with the manager's school
      filterUserDto.schoolId = this.rbacService.getEffectiveSchoolId(req.user, filterUserDto.schoolId);
    }

    return this.userService.findAll(filterUserDto);
  }

  @Get(':id')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER)
  @ApiOperation({ summary: 'Get user by ID' })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiResponse({
    status: 200,
    description: 'User retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        name: { type: 'string' },
        email: { type: 'string' },
        role: { type: 'string', enum: ['teacher', 'admin'] },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  async findOne(@Param('id') id: string, @Req() req) {
    const user = await this.userService.findById(id);

    // Business logic: School managers can only access users from their own school
    if (this.rbacService.isSchoolManager(req.user)) {
      if (!req.user.schoolId) {
        throw RbacExceptions.schoolManagerNoSchool();
      }
      if (user.schoolId !== req.user.schoolId) {
        throw RbacExceptions.schoolUsersOnly();
      }
    }

    return user;
  }

  @Patch(':id')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER)
  @ApiOperation({ summary: 'Update a user' })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiBody({ type: UpdateUserDto })
  @ApiResponse({
    status: 200,
    description: 'User updated successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        name: { type: 'string' },
        email: { type: 'string' },
        role: { type: 'string', enum: ['teacher', 'admin', 'student', 'school_manager'] },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto, @Req() req) {
    // Business logic: School managers can only update users from their own school
    if (this.rbacService.isSchoolManager(req.user)) {
      if (!req.user.schoolId) {
        throw RbacExceptions.schoolManagerNoSchool();
      }

      // Check if the user being updated belongs to the same school
      const userToUpdate = await this.userService.findById(id);
      if (userToUpdate.schoolId !== req.user.schoolId) {
        throw RbacExceptions.schoolUsersOnly();
      }

      // Validate school assignment changes
      this.rbacService.validateSchoolManagerUserAssignment(req.user, updateUserDto.schoolId);
    }

    return this.userService.update(id, updateUserDto);
  }
}
